<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'mode' => 'allow_all_except',
    'localhost_domain_list' => ['http://localhost:3000', 'http://localhost:3000/', 'http://0.0.0.0:3000', 'http://0.0.0.0:3000/', ],// domene na kojima FE pokreće lokalni razvoj
    'web_login_equals_nuxt_login' => true,
    'customer_fields' => [
        'forms' => [
            'webshop' => [
                'customer' => [
                    'auto_set_data' => true,
                    'fields' => ['first_name', 'last_name', 'email', 'address', 'zipcode', 'city', 'location', 'country', 'phone', 'message',
                        'b_same_as_shipping', 'b_first_name', 'b_last_name', 'b_address', 'b_city', 'b_zipcode', 'b_country', 'b_phone', 
                        'b_r1', 'b_company_name', 'b_company_oib', 'b_company_address',
                    ],
                    'required_fields' => ['first_name', 'last_name', 'email', 'address', 'zipcode', 'city', 'location', 'country', 'phone'],
                    'step' => 10,
                    'options' => [
                        'country' => [
                            'type' => 'function',
                            'name' => 'getWebshopOrderCountries',
                            'key' => 'id',
                            'title' => 'title',
                            'allow_empty_key' => false,
                        ],
                        'b_country' => [
                            'type' => 'function',
                            'name' => 'getWebshopOrderCountries',
                            'key' => 'id',
                            'title' => 'title',
                            'allow_empty_key' => false,
                        ],
                    ],
                ],
                'review-order' => [
                    'auto_set_data' => false,
                    'fields' => ['accept_terms'],
                    'required_fields' => ['accept_terms'],
                    'step' => 30,
                ],
            ],
            'auth' => [
                'signup' => [
                    'auto_set_data' => false,
                    'fields' => [
                        'email', 'password', 'password_confirm',
                        'first_name', 'last_name', 'birthday',
						'warehouse_location', 
						'loyalty_request', 'loyalty_code',
                        'newsletter', 'accept_terms', 'lang', 'site', 'country',
                    ],
                    'required_fields' => [
                        'email', 'password', 'password_confirm',
                        'first_name', 'last_name',
                        'accept_terms',
                    ],
                    'options' => [
                        'warehouse_location' => [
                            'type' => 'function',
                            'name' => 'getLocationPoints',
                            'key' => 'id',
                            'title' => 'title',
                            'allow_empty_key' => false,
                        ],
                    ],
                ],
                'edit' => [
                    'auto_set_data' => true,
                    'fields' => ['first_name', 'last_name', 'address', 'zipcode', 'city', 'phone', 'birthday', 'warehouse_location', 'company_name', 'company_oib', 'company_address', 'newsletter'],
                    'required_fields' => ['first_name', 'last_name', 'address', 'city', 'zipcode', 'location', 'phone'],
                ],
            ],
        ],
        'opens_fields' => [
            'b_same_as_shipping' => [
                true => [], // obavezno prvo true
                false => ['b_first_name', 'b_last_name', 'b_zipcode', 'b_city', 'b_location', 'b_address', 'b_phone'],
            ],
            'b_r1' => [
                true => ['b_company_oib', 'b_company_name', 'b_company_address'], // obavezno prvo true
                false => [],
            ],
        ],
    ],
    'custom_forms' => [
        'feedback' => [
            'satisfaction_survey_user' => [
                'fields' => ['id', 'rate', 'image_1', 'image_2', 'image_3', 'image_4', 'image_5', 'video', 'message'],
                'hidden_fields' => ['id'],
                'param_values' => ['id'],
            ],
            'satisfaction_survey_guest' => [
                'fields' => ['id', 'rate', 'image_1', 'image_2', 'image_3', 'image_4', 'image_5', 'video', 'message'],
                'hidden_fields' => ['id'],
                'param_values' => ['id'],
            ],
            'comment_guest' => [
                'fields' => ['id', 'parent_id', 'lang', 'display_name', 'email', 'rate', 'message'],
                'hidden_fields' => ['id', 'parent_id', 'lang'],
                'param_values' => ['id', 'parent_id'],
            ],
            'comment_user' => [
                'fields' => ['id', 'parent_id', 'lang', 'display_name', 'email', 'rate', 'image_1', 'image_2', 'image_3', 'image_4', 'image_5', 'video', 'message'],
                'hidden_fields' => ['id', 'parent_id', 'lang'],
                'param_values' => ['id', 'parent_id'],
            ],
        ],
    ],
    'webshop' => [
        'order_item_ignore_request_item_type' => true,
    ],
];