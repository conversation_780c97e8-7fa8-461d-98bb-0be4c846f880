<template>
	<slot :item="loyalty" :barcode="barcodePath" :loading="loading" :totals="totals" />
</template>

<script setup>
	const {getCartData} = useWebshop();
	const endpoints = useEndpoints();
	const {absolute} = useUrl();
	const cartData = computed(() => getCartData());
	const loading = ref(false);
	const barcodePath = useState('baseLoyaltyBarcode', () => null);
	const fetchedBarcode = useState('baseFetchedLoyaltyBarcode', () => false);
	const props = defineProps({
		barcode: {
			type: Boolean,
			default: false,
		},
	});

	const loyalty = computed(() => {
		const data = {};
		if (cartData.value?.cart?.loyalty) {
			data = {
				...cartData.value?.cart?.loyalty,
			};
		}
		return Object.keys(data).length ? data : null;
	});

	const totals = computed(() => {
		return {
			total_extra_loyalty: Number(cartData.value?.total?.total_extra_loyalty) || 0,
			total_extra_loyalty_discount_percent: Number(cartData.value?.total?.total_extra_loyalty_discount_percent) || 0,
			total_extra_loyalty_new: Number(cartData.value?.total?.total_extra_loyalty_new) || 0,
			total_extra_loyalty_new_discount_percent: Number(cartData.value?.total?.total_extra_loyalty_new_discount_percent) || 0,
		};
	});

	if (props.barcode && !barcodePath.value) {
		const stopWatcher = watch(
			loyalty,
			async newData => {
				if (newData?.code && !fetchedBarcode.value) {
					fetchedBarcode.value = true;
					loading.value = true;
					const res = await useApi(
						endpoints.get('_post_hapi_misc_barcode'),
						{
							method: 'POST',
							body: {
								code: newData.code,
							},
						},
						{
							cache: true,
						}
					);
					barcodePath.value = res.data?.barcode_path ? absolute(res.data.barcode_path) : null;
					loading.value = false;
					stopWatcher();
				}
			},
			{immediate: true}
		);
	}
</script>
