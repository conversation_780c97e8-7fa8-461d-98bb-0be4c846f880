<template>
	<article class="cp cp-list cp-list-small" :class="[{'cp-unavailable': !item.is_available}]">
		<div class="cp-col cp-col1">
			<div class="cp-brand" v-if="item?.manufacturer_id?.length">
				<BaseUiImage loading="lazy" :src="item.manufacturer_main_image_upload_path" default="/images/no-image-50.jpg" />
			</div>

			<div class="cp-image">
				<div class="cp-badges">
					<template v-if="b2b">
						<span class="cp-badge cp-badge-discount" :class="{'special': itemsAttributes?.length}" v-if="((1 - item.price_b2b_custom / item.basic_price_b2b_custom) * 100).toFixed(0) > 0">-{{ ((1 - item.price_b2b_custom / item.basic_price_b2b_custom) * 100).toFixed(0) }}%</span>
					</template>
					<template v-else>
						<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
							<template v-if="loyalty">
								<div class="cp-badge cp-badge-discount" v-if="loyalty.discount_percent > item.discount_percent > 0">-{{loyalty.discount_percent}}%</div>
								<div class="cp-badge cp-badge-discount" v-else>-{{item.discount_percent_custom}}%</div>
							</template>
							<template v-else>
								<div class="cp-badge cp-badge-discount" v-if="item?.discount_percent_custom && item.discount_percent_custom > 0">-{{item.discount_percent_custom}}%</div>
								<span class="cp-badge cp-badge-special" v-if="bestBuy"><BaseCmsLabel code="best_buy" tag="span" /></span>
								<BaseCmsLabel code="badge_new" tag="div" class="cp-badge cp-badge-new" v-if="item?.priority_details?.code == 'new' && !bestBuy" />
							</template>
						</WebshopLoyalty>
					</template>
				</div>
				<div class="cp-badges">
					<span class="cp-badge cp-badge-action cp-badge-pickup" v-if="item.type == 'pickup'">
						<span><BaseCmsLabel code="pickup_only" default="Samo u poslovnici" /> ({{ availablePickupLocations?.length }})</span>
					</span>
				</div>

				<NuxtLink :to="item.url_without_domain" class="cp-main-image" target="_parent">
					<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width144-height144-crop1']" default="/images/no-image-144.jpg" :title="item.main_image_title" :alt="item.main_image_description || item.title" />
				</NuxtLink>
			</div>
		</div>
		<div class="cp-col cp-col2">
			<div class="cp-cnt">
				<ClientOnly>
					<!-- Rating -->
					<template v-if="item?.feedback_comment_widget && item.feedback_comment_widget.comments_status != 1">
						<div class="cp-rate cp-rate-cnt">
							<FeedbackRates :rates="item.feedback_rate_widget.rates" mode="cp-rates" v-if="item.feedback_rate_widget.rates_votes > 0" />
							<div class="cp-rate-counter" v-if="item.feedback_comment_widget?.comments > 0">
								<span class="num">({{ item.feedback_comment_widget.comments ? item.feedback_comment_widget.comments : 0 }})</span>
							</div>
						</div>
					</template>
				</ClientOnly>

				<NuxtLink :to="item.category_url_without_domain" class="cp-category" target="_parent">{{ item.category_title }}</NuxtLink>
				<div class="cp-title">
					<NuxtLink :to="item.url_without_domain" target="_parent">{{ item.title }}</NuxtLink>
				</div>
			</div>

			<div class="cp-adtc-container">
				<ClientOnly>
					<!-- Wishlist -->
					<CatalogSetWishlist :item="item" :mode="mode" v-if="mode != 'bought_together' && mode != 'wishlist'" />
				</ClientOnly>
				<div class="cp-price">
					<template v-if="b2b">
						<div class="cp-current-price"><BaseUtilsFormatCurrency :price="item.price_b2b_custom" /></div>
					</template>
					<template v-else>
						<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
							<template v-if="loyalty?.active && item.type != 'coupon'">
								<div class="old-price cp-old-price">
									<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
								</div>
								<div class="current-price red discount-price cp-discount-price" v-if="item.discount_percent > loyalty.discount_percent"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
								<div class="current-price red discount-price cp-discount-price" v-else><BaseUtilsFormatCurrency :price="item.loyalty_price" /></div>

								<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
									<template v-if="item.extra_price_lowest > 0">
										<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
									</template>
								</div>
							</template>
							<template v-else>
								<template v-if="item.price_custom > 0">
									<template v-if="item.discount_percent_custom > 0">
										<div class="old-price cp-old-price">
											<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
										</div>
										<div class="current-price red discount-price cp-discount-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
										<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'">
											<template v-if="item.extra_price_lowest > 0">
												<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
											</template>
										</div>
									</template>
									<template v-else>
										<div class="current-price cp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
										<div class="lowest-price cp-lowest-price" v-if="mode != 'instashop'"></div>
									</template>
								</template>
							</template>
						</WebshopLoyalty>
					</template>
				</div>

				<BaseWebshopShippingCountry v-slot="{selected}">
					<div class="cp-addtocart" :class="{'cp-addtocart-single': item.available_qty <= item.package_qty}" v-if="item.type != 'pickup' || item.type == 'pickup' && selected?.code == 'hr'">
						<template v-if="item.is_available">
							<WebshopQty :quantity="(item.package_qty > 1) ? item.package_qty : 1" :limit="item.available_qty" :item="item" class="cp-qty" :class="{'cp-qty-single': item.available_qty <= item.package_qty || item.available_qty == 1}" />
							<template v-if="mode == 'bought_together'">
								<div class="cp-checkbox" v-show="item.id != current">
									<input type="checkbox" :name="item.shopping_cart_code" :value="item" :id="item.shopping_cart_code" v-model="checkedItems" :disabled="index == 0" />
									<label :for="item.shopping_cart_code"><BaseCmsLabel code="bought_together_label" tag="span" /></label>
								</div>
							</template>
							<template v-else>
								<BaseWebshopAddToCart :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: item.qty}" v-slot="{onAddToCart, loading}">
									<button :class="['btn btn-add btn-orange cp-btn-addtocart', {'loading': loading}]" @click="onAddToCart">
										<UiLoader v-if="loading" />
										<BaseCmsLabel code="add_to_cart" tag="span" />
									</button>
								</BaseWebshopAddToCart>
							</template>
						</template>
						<template v-else>
							<BaseCmsLabel code="unavailable" tag="span" class="cp-unavailable-label" />
							<NuxtLink :to="item.url_without_domain" class="btn cp-btn-detail"><span></span></NuxtLink>
						</template>
					</div>
				</BaseWebshopShippingCountry>
				<!-- 
				<?php $package_qty = (!empty($item['package_qty'])) ? $item['package_qty'] : 1; ?>
				<div class="cp-addtocart<?php if($item['available_qty'] <= $package_qty): ?> cp-addtocart-single<?php endif; ?> <?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> pickup-not-visible<?php endif; ?><?php endif; ?>"<?php if($pickup_only): ?><?php if (Arr::get($selected_country, 'title', '') != 'Hrvatska'): ?> style="display: none;"<?php endif; ?> data-selected_country-croatia-adtc<?php endif; ?>>
					<?php if($item['is_available']): ?>	
						<div class="cp-qty<?php if($item['available_qty'] <= $package_qty): ?> cp-qty-single<?php endif; ?>">
							<a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '-', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);" class="wp-btn-qty wp-btn-dec"><span class="toggle-icon"></span></a>
							<input type="text" name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="wp-input-qty" value="<?php echo $package_qty; ?>" />
							<a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '+', 1, 0, <?php echo $package_qty; ?>, <?php echo (int) $item['available_qty']; ?>, <?php echo $package_qty; ?>);" class="wp-btn-qty wp-btn-inc"><span class="toggle-icon"></span></a>
						</div>

						<?php if($mode == 'bought_together'): ?>
							<div class="cp-checkbox">
								<input type="hidden" name="price[<?php echo $item['shopping_cart_code']; ?>]" value="<?php echo $item['price_custom']; ?>" />
								<input type="checkbox"<?php if (!empty($item_id) AND $item_id == $item['id']): ?> disabled<?php endif; ?> name="product_special_list_recommendation" id="product_special_list_recommendation-<?php echo $item['shopping_cart_code']; ?>" value="<?php echo $item['shopping_cart_code']; ?>" onclick="javascript:cmswebshop.shopping_cart.calculate_special_list('recommendation');" checked="checked" />
								<label for="product_special_list_recommendation-<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($cmslabel, 'bought_together_label'); ?></label>
							</div>
						<?php else: ?>
							<?php $ga4_code = $item['id'] . '_' . $item_list_id; ?>
							<a class="btn btn-orange cp-btn-addtocart<?php if($item['available_qty'] <= $package_qty): ?> cp-btn-addtocart-single<?php endif; ?>" data-ga4_code="<?php echo $ga4_code; ?>" title="<?php echo Arr::get($cmslabel, 'add_to_cart'); ?>"  href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'input', 1)">
								<span><?php echo Arr::get($cmslabel, 'add_to_cart'); ?></span>
							</a>
							<?php if($mode == 'instashop' AND $info['user_device'] == 'm'): ?>
								<span class="cp-add-success product_message product_message_<?php echo $item['shopping_cart_code']; ?>" style="display: none"></span>
							<?php endif; ?>
						<?php endif; ?>
					<?php else: ?>
						<span class="cp-unavailable-label"><?php echo Arr::get($cmslabel, 'unavailable'); ?></span>
						<a class="btn cp-btn-detail" href="<?php echo $item['url']; ?>"><span></span></a>
					<?php endif; ?>
				</div>
				-->
			</div>
		</div>
	</article>
</template>

<script setup>
	const {b2b, isLoggedIn, user, hasLoyalty} = useProfile();
	const props = defineProps({
		item: Object,
		mode: String,
		list: String,
		extraclass: String,
		itemListId: String,
		itemListName: String,
	});

	const itemsAttributes = computed(() => {
		if (!props.item.attributes_special) return [];
		return props.item.attributes_special.filter(item => item.attribute_code != 'posebne_oznake');
	});
	const bestBuy = computed(() => {
		if (!props.item.attributes_special) return false;
		return props.item.attributes_special.some(item => item.attribute_code === 'posebne_oznake' && item.code === 'best_buy');
	});
	const availablePickupLocations = computed(() => {
		return props.item.warehouses.filter(warehouse => warehouse.available_qty > 0);
	});
</script>

<style lang="less" scoped>
	.cp-list-small{
		display: flex; width: 100%; flex-direction: row; margin-left: 0; padding: 16px 24px 16px 16px; align-items: center;
		.cp-col1{
			flex-shrink: 0; position: relative; width: 144px;
			img{max-width: 144px; max-height: 144px;}
		}
		.cp-cnt{max-width: 240px;}
		.cp-image{height: 144px;}
		.cp-brand{height: 20px; top: 8px;}
		.cp-col2{flex-flow: unset; flex-direction: unset; padding: 8px 0 8px 25px; margin: 0; justify-content: space-between;}
		.cp-price{justify-content: flex-end; text-align: right;}
		:deep(.cp-image){
			img{margin-bottom: 0;}
		}
		.cp-badges{left: 8px; bottom: 5px;}
		.cp-badge{min-width: 44px; padding: 0 8px;}
		.cp-lowest-price{max-width: 120px;}
		.cp-rates{
			display: flex; margin-top: -2px;
			:deep(.icon-star), :deep(.icon-star-empty){
				position: relative; margin-right: 2px;
				&:before{.icon-star(); font: 12px/12px @fonti; color: #d5d9d3;}
			}
			:deep(.icon-star:before){color: @yellow;}
		}
		.cp-qty{width: 100px;}
		:deep(.cp-wishlist){margin: 0; right: -9px; top: 0;}
		.cp-adtc-container{display: flex; flex-flow: column; row-gap: 18px; width: 150px; flex-shrink: 0; padding-top: 40px;}
		.cp-addtocart:before{display: none;}
		.cp-btn-addtocart{
			width: 44px; line-height: 0; font-size: 0;
			span{
				&:before{margin: 0}
			}
		}

		@media(max-width: @m){
			padding: 12px 16px 12px 12px;
			.cp-col1{width: 116px;}
			.cp-image{height: 116px;}
			.cp-brand{top: 0; height: 18px;}
			.cp-col2{padding: 0 0 0 12px; flex-direction: column; flex-flow: column; justify-content: unset; row-gap: 12px;}
			.cp-rate{font-size: 11px;}
			.cp-rates{margin-top: -1px;}
			.cp-qty{width: 85px;}
			.cp-badges{left: 3px; bottom: 3px;}
			.cp-cnt{max-width: 100%;}
			.cp-category{padding-top: 0; padding-bottom: 2px; padding-right: 20px;}
			:deep(.cp-wishlist){
				right: -12px; top: -13px;
			}
			.cp-lowest-price{max-width: 90px;}
			.cp-addtocart{margin-top: 0; width: auto; height: 40px;}
			.cp-price{display: block; text-align: left; flex-grow: 1; margin-top: 0; padding-right: 5px;}
			.cp-adtc-container{flex-flow: row; row-gap: unset; width: 100%; padding-top: 0; align-items: center;}
			.cp-btn-addtocart{
				padding: 0; width: 40px; height: 40px; font-size: 0; left: 0; flex-grow: unset;
				span{
					display: flex; position: relative; align-items: center;
					&:before{.pseudo(auto,auto) !important; .icon-cart() !important; color: #fff !important; font: 19px/19px @fonti !important; margin-left: -10px;}
				}
			}
		}
	}
</style>
