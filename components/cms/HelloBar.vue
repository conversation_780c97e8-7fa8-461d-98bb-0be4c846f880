<template>
	<!-- 
		FIXME INTEG složiti dodavanje kupona 
		<?php
			$coupon_activated_class = '';
			if(isset($shopping_cart['total_extra_coupon_code']) AND in_array($hello_bar['coupon_code'], $shopping_cart['total_extra_coupon_code'])){
				$coupon_activated_class = 'active';
			}
		?>
	-->
	<!-- FIXME INTEG složiti provjere na liniji ispod -->
	<BaseWebshopCouponForm v-slot="{onSubmit, activeCoupon}">
		<div class="hello-bar hello-bar-live" :style="(mobileBreakpoint) ? styleMobile : styleDesktop" :class="{'active': activeCoupon?.code == item?.coupon_code}">
			<div class="wrapper">
				<div class="heb-image" v-if="item?.image_2_upload_path">
					<BaseUiImage loading="lazy" :data="item.image_2_thumbs?.['width205-height120']" default="/images/no-image-205.jpg" />
				</div>

				<div class="heb-content" :class="{'heb-content-noimage': !item.image_2_upload_path}" :style="customColor">
					<template v-if="item.link?.length">
						<NuxtLink :to="item.link" :style="customColor">
							<div class="heb-title" v-html="item.title" />
							<div class="heb-subtitle" v-html="item.title2" />
						</NuxtLink>
					</template>
					<template v-else>
						<div class="heb-title" v-html="item.title" />
						<div class="heb-subtitle" v-html="item.title2" />
					</template>
				</div>

				<!-- 
					FIXME INTEG složiti dodavanje kupona 
					<?php
						$coupon_activated_class = '';
						if(isset($shopping_cart['total_extra_coupon_code']) AND in_array($hello_bar['coupon_code'], $shopping_cart['total_extra_coupon_code'])){
							$coupon_activated_class = 'active';
						}
					?>
				-->
				<div class="heb-right-container" v-if="item?.coupon_code?.length">
					<div class="activated-coupon-note" v-html="item.element_hellobar_message_content" v-if="item.element_hellobar_message_content?.length" :style="customColor" />
					<form @submit.prevent="onSubmit()">
						<input id="coupon_code" type="hidden" :value="item.coupon_code" name="coupon_code" />
						<button class="btn btn-blue hellobar-coupon-btn" @click="onSubmit()">
							<BaseCmsLabel code="hellobar_coupon_title_inactive" tag="span" class="i" />
							<BaseCmsLabel code="hellobar_coupon_title_active" tag="span" class="a" />
						</button>
					</form>
				</div>
				<!-- 
					<?php if($hello_bar['coupon_code'] != null) : ?>
						<div class="heb-right-container">
							<?php  if($hello_bar['coupon_code'] != null) : ?>
								<input type="hidden" data-hellobar_coupon_code="<?php echo $hello_bar['coupon_code']; ?>" />
								<?php $hellobar_message_button = ''; ?>
								<?php $hellobar_message_content = ''; ?>
								<?php
									if(!empty($hello_bar['element_hellobar_message_button_link']) AND !empty($hello_bar['element_hellobar_message_button_content']) AND !empty($hello_bar['element_hellobar_message_content'])){
										$hellobar_message_button = '<a class="btn" href="'.$hello_bar['element_hellobar_message_button_link'].'">'.$hello_bar['element_hellobar_message_button_content'].'</a>';
									}
									if (!empty($hello_bar['element_hellobar_message_content'])) {
										$hellobar_message_content = '<p>' . $hello_bar['element_hellobar_message_content'] . $hellobar_message_button . '</p>';
									}
								?>
								<div class="hellobar-coupon-message" data-hellobar_message="message" data-hellobar_message_value="<?php echo htmlentities($hellobar_message_content); ?>">
								</div>
							<?php endif; ?>


							<div class="activated-coupon-note"><?php echo $hello_bar['element_hellobar_message_content'] ?></div>
							<a class="btn btn-blue hellobar-coupon-btn" href="javascript:cmscoupon.set('webshop_coupon', '<?php echo $hello_bar['coupon_code']; ?>')" data-list_link="<?php if(!empty($hello_bar['element_hellobar_message_button_link'])): ?><?php echo $hello_bar['element_hellobar_message_button_link']; ?><?php endif; ?>">
								<span class="i"><?php echo Arr::get($cmslabel, 'hellobar_coupon_title_inactive'); ?></span>
								<span class="a"><?php echo Arr::get($cmslabel, 'hellobar_coupon_title_active'); ?></span>
							</a>
						</div>
					<?php endif; ?>
				-->

				<template v-if="counterEnds?.length">
					<BaseUiCountdown :end="counterEnds" v-slot="{days, hours, minutes, seconds, ended}">
						<div class="heb-right-timer" v-if="!ended" :style="customColor">
							<div class="heb-right">
								<div v-if="days && days > 1" class="day">
									<span>{{ days }}</span> d
								</div>
								<div class="hour">
									<span>{{ hours }}</span> h
								</div>
								<div class="min">
									<span>{{ minutes }}</span> m
								</div>
								<div class="sec">
									<span>{{ seconds }}</span> s
								</div>
							</div>
						</div>
					</BaseUiCountdown>
				</template>
			</div>
		</div>
	</BaseWebshopCouponForm>
	<!-- 
		<?php $hello_bar_rotator = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'hello_bar', 'limit' => 1, 'ignore_hidden_element' => true]); ?>
		<?php if(!empty($hello_bar_rotator)): ?>
			<?php foreach ($hello_bar_rotator as $hello_bar): ?>
				<?php
					$coupon_activated_class = '';
					if(isset($shopping_cart['total_extra_coupon_code']) AND in_array($hello_bar['coupon_code'], $shopping_cart['total_extra_coupon_code'])){
						$coupon_activated_class = 'active';
					}
				?>
				<style>
					<?php if(!empty($hello_bar['image'])): ?>
						.hello-bar-live{background-image: url(<?php echo Utils::file_url($hello_bar['image']); ?>); background-repeat: no-repeat;}
						@media (max-width: 950px){
							<?php if(!empty($hello_bar['image_3'])): ?>
								.hello-bar-live{background-image: url(<?php echo Utils::file_url($hello_bar['image_3']); ?>); background-repeat: no-repeat;}
							<?php endif; ?>
						}
					<?php elseif(!empty($hello_bar['color2']) && strlen($hello_bar['color']) === 7): ?>
						.hello-bar-live{background: <?php echo $hello_bar['color2']; ?>;}
					<?php endif; ?>

					<?php if(!empty($hello_bar['color']) && strlen($hello_bar['color']) === 7): ?>
						.heb-content, .heb-content a, .heb-right,.activated-coupon-note{color: <?php echo $hello_bar['color']; ?>;}
					<?php endif; ?>
				</style>
				<div class="hello-bar hello-bar-live<?php if($hello_bar['coupon_code'] != null AND !empty($user->b2b)) : ?> hello-bar-none<?php endif; ?><?php if($hello_bar['coupon_code'] == null AND (empty($hello_bar['datetime_counter']) AND empty($hello_bar['date_active_to']))) : ?> hello-bar-clear<?php endif; ?> <?php echo $coupon_activated_class; ?>" data-coupon_active="webshop_coupon">
					<div class="wrapper">
						<?php if(!empty($hello_bar['image_2'])): ?>
							<div class="heb-image">
								<span >
									<?php $fileExtension = pathinfo($hello_bar['image_2'], PATHINFO_EXTENSION); ?>
									<?php if($fileExtension == 'svg'): ?>
										<img loading="lazy" src="<?php echo Utils::file_url($hello_bar['image_2']); ?>" alt="">
									<?php else: ?>
										<img loading="lazy" <?php echo Thumb::generate($hello_bar['image_2'], array('width' => 205, 'height' => 120, 'default_image' => '/media/images/no-image-205.jpg', 'html_tag' => TRUE)); ?> alt="" />
									<?php endif; ?>
								</span>
							</div>
						<?php endif; ?>
						<div class="heb-content<?php if(empty($hello_bar['image_2'])): ?> heb-content-noimage<?php endif; ?>">
							<?php if(!empty($hello_bar['link'])): ?>
								<a href="<?php echo $hello_bar['link']; ?>">
							<?php endif; ?>
								<div class="heb-title">
									<?php echo $hello_bar['title']; ?>
								</div>
								<div class="heb-subtitle">
									<?php echo $hello_bar['title2']; ?>
								</div>
							<?php if(!empty($hello_bar['link'])): ?>
								</a>
							<?php endif; ?>
						</div>
						<?php if($hello_bar['coupon_code'] != null) : ?>
							<div class="heb-right-container">
								<?php  if($hello_bar['coupon_code'] != null) : ?>
									<input type="hidden" data-hellobar_coupon_code="<?php echo $hello_bar['coupon_code']; ?>" />
									<?php $hellobar_message_button = ''; ?>
									<?php $hellobar_message_content = ''; ?>
									<?php
										if(!empty($hello_bar['element_hellobar_message_button_link']) AND !empty($hello_bar['element_hellobar_message_button_content']) AND !empty($hello_bar['element_hellobar_message_content'])){
											$hellobar_message_button = '<a class="btn" href="'.$hello_bar['element_hellobar_message_button_link'].'">'.$hello_bar['element_hellobar_message_button_content'].'</a>';
										}
										if (!empty($hello_bar['element_hellobar_message_content'])) {
											$hellobar_message_content = '<p>' . $hello_bar['element_hellobar_message_content'] . $hellobar_message_button . '</p>';
										}
									?>
									<div class="hellobar-coupon-message" data-hellobar_message="message" data-hellobar_message_value="<?php echo htmlentities($hellobar_message_content); ?>">
									</div>
								<?php endif; ?>


								<div class="activated-coupon-note"><?php echo $hello_bar['element_hellobar_message_content'] ?></div>
								<a class="btn btn-blue hellobar-coupon-btn" href="javascript:cmscoupon.set('webshop_coupon', '<?php echo $hello_bar['coupon_code']; ?>')" data-list_link="<?php if(!empty($hello_bar['element_hellobar_message_button_link'])): ?><?php echo $hello_bar['element_hellobar_message_button_link']; ?><?php endif; ?>">
									<span class="i"><?php echo Arr::get($cmslabel, 'hellobar_coupon_title_inactive'); ?></span>
									<span class="a"><?php echo Arr::get($cmslabel, 'hellobar_coupon_title_active'); ?></span>
								</a>
							</div>
						<?php endif; ?>
						<?php if(!empty($hello_bar['datetime_counter']) OR !empty($hello_bar['date_active_to'])): ?>
							<div class="heb-right-timer">
								<?php if(!empty($hello_bar['datetime_counter'])): ?>
									<div class="heb-right" data-countdown="<?php echo date('Y/m/d H:i:s', $hello_bar['datetime_counter']); ?>"></div>
								<?php elseif(!empty($hello_bar['date_active_to'])): ?>
									<div class="heb-right" data-countdown="<?php echo date('Y/m/d H:i:s', $hello_bar['date_active_to']); ?>"></div>
								<?php endif; ?>
							</div>
						<?php endif; ?>
					</div>
						<div class="heb-close"></div>
				</div>
			<?php endforeach; ?>
		<?php endif; ?>
	-->

	<!--
	<div v-if="items?.length && !isHidden" class="hello">
		<div class="hello-wrapper wrapper" v-for="item in items" :key="item.id">
			<div class="hello-cnt">
				<div class="hello-image" :class="{'hello-image-mobile':mobileSmallBreakpoint}" v-if="item.image_upload_path">
					<div class="hello-close" @click="close" v-if="mobileSmallBreakpoint"></div>
					<BaseUiImage :data="item.image_thumbs?.['width160-height60-crop1']" default="/images/no-image-160.jpg" loading="lazy" :picture="[{maxWidth: '755px', src: item.image_2_thumbs?.['width755-height133']?.thumb, default: '/images/no-image-755.jpg'}]" />
				</div>
				<div class="hello-content">
					<span v-html="item.title"></span>
					<template v-if="item.title2 && item.url_without_domain">
						<NuxtLink :to="item.url_without_domain" class="hello-link">{{item.title2}}</NuxtLink>
					</template>
				</div>
				<template v-if="item?.datetime_counter">
					<BaseUiCountdown :end="item.datetime_counter" v-slot="{days, hours, minutes, seconds, ended}">
						<div class="hello-counter" v-if="!ended">
							<div v-if="days && days > 1" class="day">
								<span>{{ days }}</span> d
							</div>
							<div class="hour">
								<span>{{ hours }}</span> h
							</div>
							<div class="min">
								<span>{{ minutes }}</span> m
							</div>
							<div class="sec">
								<span>{{ seconds }}</span> s
							</div>
						</div>
					</BaseUiCountdown>
				</template>
			</div>

			<div class="hello-close" @click="close" v-if="!mobileSmallBreakpoint || mobileSmallBreakpoint && !item.image_upload_path"></div>
		</div>
	</div>
	-->
</template>

<script setup>
	const props = defineProps(['items']);
	const {b2b} = useProfile();
	const {mobileBreakpoint} = inject('rwd');
	const {absolute} = useUrl();
	const item = props.items[0];

	const styleDesktop = computed(() => {
		if (item.image_upload_path) {
			return {
				backgroundImage: `url(${absolute(item.image_upload_path)})`,
				backgroundRepeat: 'no-repeat',
			}
		} else if (item.color2 && item.color2?.length === 7) {
			return { background: item.color2, }
		} else {
			return {}
		}
	});

	const styleMobile = computed(() => {
		if (item.image_3_upload_path) {
			return {
				backgroundImage: `url(${absolute(item.image_3_upload_path)})`,
				backgroundRepeat: 'no-repeat',
			}
		} else if (item.color2 && item.color2?.length === 7) {
			return { background: item.color2, }
		} else {
			return {}
		}
	});

	const customColor = computed(() => {
		if (item.color && item.color?.length === 7) {
			return { color: item.color, }
		} else {
			return {}
		}
	});

	const counterEnds = computed(() => {
		if (item.date_active_to) {
			return item.date_active_to;
		} else {
			return {}
		}
	});

	item.date_active_to

	/*
	const helloBarStyle = computed(() => {
		if (item.image_upload_path) {
			if(!mobileBreakpoint){
				console.log('1')
				return {
					backgroundImage: `url(${absolute(item.image_upload_path)})`,
					backgroundRepeat: 'no-repeat',
				}
			}else{
				console.log('1')
				return {
					backgroundImage: `url(${absolute(item.image_3_upload_path)})`,
					backgroundRepeat: 'no-repeat',
				}
			}
		} else if (item.color2 && item?.length === 7) {
			return { background: item.color2, }
		} else {
			return {}
		}
	})
	*/
</script>
