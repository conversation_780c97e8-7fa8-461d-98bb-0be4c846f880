<template>
	<!-- , response_fields: ['id','link','url_without_domain','title','content','image_upload_path','image_thumbs','image_2_upload_path','image_2_thumbs','datetime_counter', 'title2']-->
	<BaseCmsRotator @loadRotatorItems="onLoad" :fetch="{code: 'hello_bar', limit: 1}" v-slot="{items}">
		<CmsHelloBar :items="items" v-if="items?.length" />
	</BaseCmsRotator>

	<WebshopLoyalty v-slot="{loyalty}">
		<div class="loyalty-quick" v-if="loyalty?.active">
			{{barcode}}
			<div class="btn-loyalty-header" :class="{'active': loyaltyDropdown}" @click="loyaltyDropdown = !loyaltyDropdown"><span class="s">Prikaži</span><span class="h">Sakrij</span> loyalty klub karticu</div>
			<div class="loyalty-header" :class="{'active': loyaltyDropdown}">
				<!-- FIXME INTEG provjeriti što s loyalty barcodom -->
				<!--<img src="<?php echo Utils::site_url(Kohana::config('app.language')) . $barcode; ?>" alt="">-->
				<div style="width:100%;text-align: center;letter-spacing: 0.55em;padding-left:0.55em">{{loyalty.code}}</div>
			</div>
		</div>
	</WebshopLoyalty>
</template>

<script setup>
	const loyaltyDropdown = ref(false);

	const isHidden = ref(true);
	let rotatorItemId = 0;


	function onLoad(data) {
		rotatorItemId = data?.items?.[0]?.id;
		const cookie = useCookie('hellobar_new');
		if(!cookie.value) {
			isHidden.value = false;
		}
	}

	function close() {
		const cookie = useCookie('hellobar_new', {
			maxAge: 60 * 60 * 24 * 30 // 1 month
		});
		cookie.value = 1;
		isHidden.value = true;
	}
</script>
