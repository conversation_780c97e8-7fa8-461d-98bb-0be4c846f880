<template>
	<!-- FIXME INTEG - Dodati LAZY isped CmsNewsletter dijela kasnije -->
	<CmsNewsletter hydrate-never v-if="showNewsletter" />

	<LazyCmsBottom v-if="showElement" />

	<LazyCmsFooter />

	<ClientOnly>
		<BaseThemeUiPageLoadingIndicator track-color="#003160" />
		<LazyBaseThemeUiModal v-if="Object.keys(modal.activeModals()).includes('quick')" name="quick" :zoom="false" :mask-closable="true" :svgicons="false" />
		<NewsletterLeaving hydrate-on-visible />
	</ClientOnly>

	<PublishInstashopModal :instashopData="instashopData" />

	<UiOntop />
</template>

<script setup>
	const modal = useModal();
	const route = useRoute();
	const props = defineProps(['instashopData', 'showElement']);
	const showNewsletter = computed(() => {
		if (!route?.meta?.template || !route?.meta?.contentType) return true;
		if (!route?.meta?.action) return true;

		if (route.meta.controller === 'webshop') {
			const actionsToHide = ['login', 'customer', 'shipping', 'payment', 'review_order'];
			return !actionsToHide.includes(route.meta.action);
		}

		if (route.meta.contentType === 'auth') {
			const actionsToHide = ['AuthDefault','AuthMyWebshoporder', 'AuthWishlist', 'AuthMyWebshopcoupon', 'AuthLogin', 'AuthSignup', 'AuthForgottenPassword', 'AuthEdit', 'AuthChangePassword'];
			return !actionsToHide.includes(route.meta.template);
		}
		return true;
	});
</script>

<style lang="less" scoped>
	:deep(.base-modal-gallery){background: #fff;}
	:deep(.base-modal-gallery-nav-btn){
		display: flex; align-items: center; justify-content: center;
		&:before{.icon-arrow-right(); font: 40px/1 @fonti; color: @black; .rotate(180deg); .transition(color);}
		@media (min-width: @h){
			&:hover:before{color: @lightGreen;}
		}
	}
</style>
