<template>
	<Body class="page-publish-index page-recipes" :class="{'white-bg page-search': contentType == 'search', 'active-filter': publishActiveFilters}" />
	<BasePublishCategory :root-category="true" :include-subcategories="true" v-slot="{item: category, rootCategory, contentType}" :seo="true">
		<BasePublishPosts v-slot="{items: posts, nextPage, loadMore, loading}">
			<div class="wrapper">
				<div class="df p-recipes-row">
					<!-- FIXME - potrebno vidjeti kakva su tu neka preračunavanja vezana za hellobar jednom kad se hellobar napravi
					
					<?php $hello_bar_rotator = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'hello_bar', 'limit' => 1, 'ignore_hidden_element' => true]); ?>
					<?php $hello_bar_rotator_hidden = false; ?>
					<?php if(!empty($hello_bar_rotator)): ?>
						<?php foreach ($hello_bar_rotator as $hello_bar): ?>
							<?php if($hello_bar['coupon_code'] != null AND !empty($user->b2b)) : ?>
								<?php $hello_bar_rotator_hidden = true; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php endif; ?>

					<aside class="p-recipes-sidebar<?php if (!empty($hello_bar_rotator)): ?> recipes-sidebar-hellobar<?php endif; ?><?php if($hello_bar_rotator_hidden): ?> hello-bar-none<?php endif; ?>">
						<?php $search_fields = Widget_Publish::search_filters(array('lang' => $info['lang'], 'filters' => $filters, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1))); ?>
						<?php $active_filters = (!empty($search_fields['_basic']['selected'])) ? $search_fields['_basic']['selected'] : []; ?>
						<?php if (count($items) > 0 OR $active_filters): ?>
							<?php echo View::factory('catalog/widget/filter', ['search_fields' => $search_fields, 'active_filters' => $active_filters, 'class' => 'cf-recipes', 'mode' => 'recipes']); ?>
						<?php endif; ?>
					</aside> -->

					<aside class="p-recipes-sidebar">
						<ClientOnly>
							<BasePublishFilters v-slot="{searchFields, selectedFiltersCounter}">
								<div class="cf cf-recipes">
									<div class="cf-filter-header" @click="closeFilters" v-if="mobileBreakpoint">
										<span><span class="filter-icon"></span><BaseCmsLabel code="filters" /></span>
										<span class="cf-filter-close"><span class="cf-filter-close-icon"></span></span>
									</div>
									<div class="cf-body">
										<BasePublishActiveFilters v-slot="{items, onRemove}">
											<div class="cf-active" v-if="items?.length">
												<BaseCmsLabel class="cf-active-title" code="selected_filters" tag="div" />
												<template v-for="item in items" :key="item.id">
													<div class="cf-active-item">
														<span class="cf-active-item-link" @click="onRemove(item)">{{item.title}}</span>
													</div>
												</template>
												<div class="cf-active-btns">
													<BaseCmsLabel class="btn-cf-active-clear" code="clear_filtering" tag="span" @click="onRemove()" />
												</div>
											</div>
											<div class="cf-btns">
												<div class="btn btn-gray btn-m-filter btn-m-cf-active-clear cf-btn-clear" v-if="items?.length" @click="onRemove(), closeFilters()"><BaseCmsLabel code="clear_active_filters" /></div>
												<div class="btn cf-btn-bottom cf-btn-confirm" @click="closeFilters"><BaseCmsLabel tag="span" code="filters_confirm" /></div>
											</div>
										</BasePublishActiveFilters>
										<div class="cf-items">
											<BasePublishFilterItem v-for="filter in searchFields" :key="filter.id" :item="filter" v-slot="{fields, onToggle, totalFields, onFilter, onClear, selectedFilters, active}">
												<template v-if="filter.code != 'category_id'">
													<div class="cf-item" :class="['cf-item-' + filter.code, {'active': !active}]">
														<div class="cf-title" :class="['cf-title-' + filter.code]" @click="onToggle">
															{{filter.label}}
															<span class="toggle-icon"></span>
														</div>
														<div class="cf-item-wrapper">
															<div class="cf-row" v-for="field in fields" :key="field.id">
																<input type="checkbox" :name="filter.filter_url" :id="field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
																<label :for="field.unique_code">
																	{{field.title}}
																	<span class="cf-counter">{{ field.total_available }}</span>
																</label>
															</div>
														</div>
													</div>
												</template>
											</BasePublishFilterItem>
										</div>
									</div>
								</div>
							</BasePublishFilters>
						</ClientOnly>
					</aside>
					<!-- <div class="cf-clear" @click="onClear" style="display: none;"></div> -->

					<div class="p-recipes-main" v-if="posts?.length">
						<div class="p-items">
							<PublishIndexEntryRecipes v-for="post in posts" :key="post.id" :item="post" />
						</div>
						<ClientOnly>
							<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
								<button type="button" class="btn load-more btn-load-more btn-load-more-recipes" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel tag="span" code="load_more_recipes" /></button>
							</div>
						</ClientOnly>
						<BaseUiPagination class="pagination" />
					</div>
					<div v-else class="p-empty"><BaseCmsLabel code="no_publish" /></div>
				</div>
			</div>
		</BasePublishPosts>
	</BasePublishCategory>
</template>

<script setup>
	const props = defineProps(['contentType']);
	const {mobileBreakpoint} = inject('rwd');
	const publishActiveFilters = useState('publishActiveFilters', () => false);
	const closeFilters = () => {
		publishActiveFilters.value = false;
	};
</script>

<style scoped lang="less">
	.cf-row{
		input[type=checkbox]+label{white-space: nowrap; font-size: 12px; width: 100%; padding-right: 30px;}
		input[type=checkbox]:checked+label{font-weight: bold;}
		&.manufacturer{
			input[type=checkbox]+label{
				padding: 6px 12px; border: 1px solid #DEDEDE; border-radius: 2px;
				&:before{display: none;}
			}
			input[type=checkbox]:checked+label{background: @lightGreen; border-color: @lightGreen; color: #fff; font-weight: normal;}
		}
	}
</style>
