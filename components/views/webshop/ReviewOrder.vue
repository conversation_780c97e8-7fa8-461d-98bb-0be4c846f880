<template>
	<Body class="main-offset-sm page-checkout page-checkout-step4 page-checkout-review-order" />
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #wcCol1>
				<div class="wc-col wc-col1 wc-step4-col1">
					<WebshopStep :step="1" :completed="true" />
					<WebshopStep :step="2" :completed="true" />
					<div class="wc-col-cnt">
						<BaseWebshopCheckout v-slot="{urls}">
							<h1 class="wc-title">{{page?.seo_h1}}</h1>
							<BaseWebshopReviewOrderForm class="step4 form-label ajax_siteform ajax_siteform_loading" v-slot="{fields, customer, shipping, payment, meta, loading, orderErrors, total}">
								<div class="global-error" v-if="orderErrors?.customer && !loading"><BaseCmsLabel :code="orderErrors.customer" /></div>
								<div class="ct-review">
									<div class="ct-review-title">
										<BaseCmsLabel code="step1" /> <NuxtLink :to="urls.webshop_customer" class="ct-review-title-btn"><BaseCmsLabel code="change" /></NuxtLink>
									</div>
									<div class="ct-review-value">
										<div v-if="customer?.first_name || customer?.last_name">{{ customer.first_name }} {{ customer.last_name }}</div>
										<div v-if="customer?.oib"><BaseCmsLabel code="oib" />: {{ customer.oib }}</div>
										<div v-if="customer?.address?.street">{{ customer.address.street }}</div>
										<div v-if="customer?.address?.zipcode || customer?.address?.city">{{ customer.address.zipcode }} {{ customer.address.city }}</div>
										<div v-if="customer?.phone">{{ customer.phone }}</div>
										<div v-if="customer?.address?.country_name">{{ customer.address.country_name }}</div>
										<div v-if="customer?.email">{{ customer.email }}</div>
										<div v-if="customer?.message">{{ customer.message }}</div>
									</div>
								</div>

								<template v-if="customer?.b_first_name">
									<div class="ct-review">
										<div class="ct-review-title">
											<BaseCmsLabel code="bill_address" /> <NuxtLink :to="urls.webshop_customer" class="ct-review-title-btn"><BaseCmsLabel code="change" /></NuxtLink>
										</div>
										<div v-if="customer?.b_first_name || customer?.b_last_name">{{ customer.b_first_name }} {{ customer.b_last_name }}</div>
										<div v-if="customer?.b_oib">{{ customer.b_oib }}</div>
										<div v-if="customer?.b_address?.b_street">{{ customer.b_address.b_street }}</div>
										<div v-if="customer?.b_address?.b_zipcode || customer?.b_address?.b_city">{{ customer.b_address.b_zipcode }} {{ customer.b_address.b_city }}</div>
										<div v-if="customer?.b_address?.b_country_name">{{ customer.b_address.b_country_name }}</div>
										<div v-if="customer?.b_phone">{{ customer.b_phone }}</div>
										<div v-if="customer?.b_email">{{ customer.b_email }}</div>
										<div v-if="customer?.b_message">{{ customer.b_message }}</div>
									</div>
								</template>
								<!-- <div v-else class="ct-review">
									<div class="ct-review-title">
										<BaseCmsLabel code="bill_address" tag="span" /> <NuxtLink :to="urls.webshop_customer" class="ct-review-title-btn"><BaseCmsLabel code="change" tag="span" /></NuxtLink>
									</div>
									<BaseCmsLabel code="same_as_shipping" tag="div" />
								</div> -->

								<div class="ct-review cart-total-shipping" v-if="customer?.b_company_oib">
									<div class="ct-review-title">
										<BaseCmsLabel code="r1_address" /> <NuxtLink :to="urls.webshop_customer" class="ct-review-title-btn"><BaseCmsLabel code="change" /></NuxtLink>
									</div>
									<div class="ct-review-value">
										<div><BaseCmsLabel code="company_name" />: {{ customer.b_company_name }}</div>
										<div><BaseCmsLabel code="company_oib" />: {{ customer.b_company_oib }}</div>
										<div><BaseCmsLabel code="company_address" />: {{ customer.b_company_address }}</div>
									</div>
								</div>

								<div class="ct-review cart-total-shipping">
									<div class="ct-review-title">
										<BaseCmsLabel code="shipping" /> <NuxtLink :to="urls.webshop_payment" class="ct-review-title-btn"><BaseCmsLabel code="change" /></NuxtLink>
									</div>
									<div>
										<span v-html="shipping.title"></span><template v-if="shipping?.pickup_location">: {{shipping?.pickup_location.title}}</template>
									</div>
									<div class="global-error" v-if="orderErrors?.shipping && !loading"><BaseCmsLabel :code="orderErrors.shipping" /></div>
								</div>

								<div class="ct-review cart-total-payment">
									<div class="ct-review-title">
										<BaseCmsLabel code="payment" /> <NuxtLink :to="urls.webshop_payment" class="ct-review-title-btn"><BaseCmsLabel code="change" /></NuxtLink>
									</div>
									<div>{{ payment?.title }}</div>
									<div class="global-error" v-if="orderErrors?.payment && !loading"><BaseCmsLabel :code="orderErrors.payment" /></div>
								</div>

								<div class="ct-review cart-total-payment" v-if="total">
									<BaseCmsLabel code="cart_totals" class="ct-review-title" tag="div" />
									<WebshopTotal />
								</div>

								<div class="wc-terms">
									<template v-if="fields">
										<div class="webshop-alert-terms" data-accept_terms="note" v-show="acceptTermsAlert"><BaseCmsLabel code="terms_error" tag="span" /></div>
										<div v-for="(field,index) in fields" :key="field.name" class="webshop_accept_terms webshop-accept-terms" :class="'webshop-accept-terms'+ index" v-interpolation>
											<span class="wc-accept-terms-tooltip" :class="'webshop-accept-terms'+ index"><BaseCmsLabel code="terms_required" /></span>
											<BaseFormField :item="field" v-slot="{errorMessage}">
												<BaseFormInput @click="checkAlert" />
												<template v-if="field.name == 'accept_terms'">
													<BaseCmsLabel tag="label" :for="field.name" code="accept_terms_webshop" />
												</template>
												<span class="field_error error" v-show="errorMessage" v-html="errorMessage" />
											</BaseFormField>
										</div>
									</template>

									<button :class="['btn btn-orange btn-finish', loading && 'loading']" type="submit" :disabled="!meta?.valid || loading" v-if="!orderErrors">
										<UiLoader v-if="loading" />
										<BaseCmsLabel code="confirm_order" tag="span" />
									</button>
								</div>
							</BaseWebshopReviewOrderForm>
						</BaseWebshopCheckout>
					</div>
				</div>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<script setup>
	const acceptTerms = ref(false);
	const finishOrderBtn = ref(false);
	function checkAlert(e) {
		if (e.target.name == 'accept_terms') acceptTerms.value = e.target.checked;
	}

	// show/hide alert
	const acceptTermsAlert = computed(() => {
		return acceptTerms.value ? false : true;
	});

	// show/hide alert on page load
	onMounted(() => {
		const acceptTermsCheckbox = document.querySelector('[name="accept_terms"]');
		if (acceptTermsCheckbox) acceptTerms.value = acceptTermsCheckbox?.checked;
	});
</script>
