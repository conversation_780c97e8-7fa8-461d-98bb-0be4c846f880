<template>
	<BaseCmsPage>
		<Body class="page-cart white-bg" :class="['cart-layout-page']" />
		<ClientOnly>
			<BaseWebshopCart v-slot="{parcels, urls, cart, onFinishShopping}" :remarketing="true">
				<BaseWebshopFreeShipping v-slot="{item: toFree}">
					<div class="wrapper" v-if="parcels?.length">
						<template v-if="parcels[0]?.items?.length">
							<div class="df w-row" id="view_cart">
								<div class="w-col w-col1">
									<div class="w-col1-cnt">
										<BaseWebshopCartErrors v-slot="{errorsItems, warningsItems}">
											<template v-if="errorsItems?.length">
												<div class="global-error" v-for="error in errorsItems" :key="error"><BaseCmsLabel :code="error.label_name" /></div>
											</template>
											<template v-if="warningsItems?.length">
												<div class="global-warning" v-for="warning in warningsItems" :key="warning"><BaseCmsLabel :code="warning.label_name" /></div>
											</template>
										</BaseWebshopCartErrors>

										<!-- <?php
											// Provjeri da li je kupac B2b bez obzira na to da li je prijavljen ili ne
											$client_email = '';
											$client_is_b2b = false;

											if (!empty($info['user_email'])) {
												$client_email = $info['user_email'];
											} elseif (!empty($user->email)) {
												$client_email = $user->email;
											} elseif (!empty($customer_data['email'])) {
												$client_email = $customer_data['email'];
											}

											if (!empty($client_email)) {
												$client_is_b2b = User::data_single($client_email, 'b2b', 'email');
											}
										?> -->
										<WebshopLoyaltyNew />

										<div id="items_shoppingcart">
											<WebshopShippingLimit />
										</div>

										<WebshopFreeDeliveryList v-if="cart" :toFree="toFree" :cart="cart" />
										<WebshopCartWishlist mode="cart" :parcels="parcels" />
									</div>
								</div>
								<div class="w-col w-col2">
									<div class="w-col2-cnt-top"></div>
									<div class="w-col-cnt w-col2-cnt">
										<div class="w-col2-all-cnt">
											<WebshopCouponForm mode="cart" />
											<WebshopPriorityOrder />

											<BaseCmsLabel code="cart_total" class="cart-totals-title" tag="div" />
											<WebshopTotal />

											<BaseWebshopCartErrors v-slot="{errors, warnings}">
												{{errors}}
												<NuxtLink @click="onFinishShopping" v-if="urls.webshop_customer && !warnings" :to="urls.webshop_customer" class="btn btn-orange w-btn-finish cart-finish-shopping" :class="{'disabled': errors}"
													><BaseCmsLabel code="finish_shopping" tag="span"
												/></NuxtLink>
												<BaseCmsLabel code="unable_to_complete_order_2" tag="div" class="wp-unable-to-completeorder" v-if="errors" />
											</BaseWebshopCartErrors>

											<WebshopFreeDeliveryProgressBar :parcels="parcels" :toFree="toFree" />
										</div>
									</div>
								</div>
							</div>
						</template>

						<BaseCmsLabel v-else code="empty_shopping_cart" class="empty-cart" tag="div" />
					</div>
				</BaseWebshopFreeShipping>
			</BaseWebshopCart>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const props = defineProps(['b2b', 'parcels']);
	const labels = useLabels();

	/* const {prependTo, onMediaQuery} = useDom();
		onMediaQuery({
		query: '(max-width: 980px)',
		enter: () => {
			prependTo('.free-delivery-widget', '.w-col2');
		}
	}); */
</script>
