<template>
	<div class="qty-container">
		<BaseThemeWebshopQty :quantity="props.quantity" :limit="props.limit" :item="props.item" :steps="(props.package_qty > 1) ? props.package_qty : 1" v-model="item.qty" :mode="props.mode" />
	</div>
</template>

<script setup>
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		mode: {
			type: String,
		},
		limit: [Number, String],
		quantity: [Number, String],
	})
</script>
