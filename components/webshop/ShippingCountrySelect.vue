<template>
	<div class="country-select-modal">
		<div class="country-select-modal-inner">
			<BaseCmsLabel code="pick_country_title" tag="div" class="csm-title" />
			<slot name="closeCountrySelect" />

			<BaseWebshopShippingCountry v-slot="{items: countries, submitCountry, selected, loading}">
				<BaseWebshopCart v-slot="{parcels}" :remarketing="true">
					<div class="shipping-country-select-cnt">
						<select v-if="countries?.length" @change="submitCountry({id: $event.target.value})" class="shipping-country-select">
							<option v-for="country in countries" :key="country.id" :value="country.id" :selected="country.selected">{{country.title}}</option>
						</select>

						<BaseCmsLabel code="shipping_country_note" tag="div" class="shipping-country-select-note" />
						<slot name="submitCountrySelect" />
					</div>

					<template v-if="selected?.code != 'hr'">
						<template v-if="parcels[0]?.items?.length && parcels[0]?.items.some(item => item.type == 'pickup')">
							<div class="ww-cart-items">
								<div class="wp-pickup-products">
									<BaseCmsLabel code="pickup_info" tag="div" class="status-info-label" />
									<BaseCmsLabel code="country_select_shipping_note" tag="div" class="wp-pickup-label" />
								</div>

								<div class="ww-cart-items-inner" v-if="parcels[0]?.items?.length">
									<template v-for="item in parcels[0].items" :key="item.shopping_cart_code"> <WebshopCartItem :data="item" v-if="item.type == 'pickup'" mode="checkout" /> </template>
								</div>
							</div>

							<div class="country-cart-cnt">
								<BaseWebshopRemoveProduct v-slot="{onRemove, loading}" :item="parcels[0].items.filter(item => item.type === 'pickup')">
									<span class="btn btn-orange" :class="{'loading': loading}" @click="() => { onRemove(); emit('change-country') }"><BaseCmsLabel code="clear_cart_pickup_items" tag="span" /></span>
								</BaseWebshopRemoveProduct>

								<slot name="btnChangeCountryBottom" />
							</div>
						</template>
					</template>
				</BaseWebshopCart>
			</BaseWebshopShippingCountry>
		</div>
	</div>
	<!--
	<div class="country-select-modal" data-country_select_alert_main_el style="<?php  if (empty($unavailable_items)): ?>display:none;<?php endif; ?>">
      
		<div class="csm-title"><?php echo Arr::get($cmslabel, 'pick_country_title'); ?></div>
        <div data-shipping_country_box style="display: none;">
            <?php $countries = Webshop::countries(['lang' => $info['lang']]); ?>
            <?php $selected_country = Arr::get($customer_data, 'country', ''); ?>
            <div class="shipping-country-select-cnt">
                <select class="shipping-country-select" name="shipping_country_select" data-shipping_country_select_el>
                    <?php foreach ($countries as $country_id => $country): ?>
                        <option value="<?php echo $country_id; ?>" <?php if ($country_id == $selected_country): ?>selected<?php endif; ?>><?php echo $country['title']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="shipping-country-select-note"><?php echo Arr::get($cmslabel, 'shipping_country_note'); ?></div>
            <button class="btn btn-orange shipping-country-submit" name="submit_shipping_country" data-shipping_country_select_btn><?php echo Arr::get($cmslabel, 'country_select_confirm_btn'); ?></button>
        </div>


        <div class="ww-cart-items" <?php if (empty($unavailable_items)): ?>style="display:none;"<?php endif; ?> data-country_change_response_el>
            <div class="wp-pickup-products">
                <div class="status-info-label"><?php echo Arr::get($cmslabel, 'pickup_info'); ?></div>
                <div class="wp-pickup-label"><?php echo Arr::get($cmslabel, 'country_select_shipping_note', 'U košarici se nalaze proizvodi za koje je moguća samo Wolt dostava (Hrvatska) ili osobno preuzimanje u odabranoj dostupnoj poslovnici'); ?></div>
            </div>
            <div data-unavailable_items_el <?php if (Arr::get($info, 'controller', '') == 'webshop'): ?> data-empty_cart_redirect_url="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>" <?php endif; ?>>
                <?php $shopping_cart_data = Webshop::shopping_cart_data($info); ?>
                <?php $products_status = Arr::get($shopping_cart_data, 'products_status', []); ?>
                <?php $product_codes = []; ?>
                <?php if (!empty($shopping_cart_data['products'])): ?>
                    <?php $i = 1; ?>
                    <?php foreach ($shopping_cart_data['products'] as $shopping_cart_code => $product_data): ?>
                        <?php $product_type = Arr::get($product_data, 'type', ''); ?>
                        <?php $product_status = Arr::get($products_status, $shopping_cart_code, []); ?>
                        <?php if (!in_array($product_type, Kohana::config('app.catalog.product_type.pickup_only')) OR empty($product_status)) {continue;} ?>
                        <?php array_push($product_codes, $shopping_cart_code); ?>
                        <?php if ($check_products): ?>
                            <div class="ww-cart-items-inner" data-pickup_cart_item="<?php echo $shopping_cart_code; ?>" <?php if (empty($unavailable_items)): ?>style="display:none;"<?php endif; ?>>
                                <?php echo View::factory('webshop/shopping_cart_entry', ['product_status' => $product_status, 'product_code' => $shopping_cart_code, 'product_data' => $product_data, 'i' => $i, 'mode' => 'checkout']); ?>
                            </div>
                        <?php endif; ?>
                        <?php $i++; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <div class="country-cart-cnt">
                <a class="btn btn-orange" style="pointer-events: none;" data-unavailable_items_remove_btn href="javascript:cmswebshop.shopping_cart.remove_all('<?php echo implode(',', $product_codes); ?>');" class="btn"><span><?php echo Arr::get($cmslabel, 'clear_cart_pickup_items'); ?></span></a>
                <a class="btn-autochange" href="javascript:setShippingCountry(1, true);"><?php echo Arr::get($cmslabel, 'autochange_croatia', 'ili promjeni državu dostave u: Hrvatska'); ?></a>
            </div>
        </div>
        <a title="Close" class="fancybox-item fancybox-close shipping-country-close" href="javascript:;"></a>
		
    </div>
	-->
</template>

<script setup>
	//const props = defineProps(['countries', 'submitCountry', 'selected', 'loading']);
	const emit = defineEmits(['change-country']);
</script>

<style lang="less" scoped></style>
